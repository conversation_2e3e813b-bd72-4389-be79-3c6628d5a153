<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aquaknow - Fish Identification & Education Platform</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="login-page">
    <div class="ocean-background">
        <div class="wave wave1"></div>
        <div class="wave wave2"></div>
        <div class="wave wave3"></div>
    </div>

    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-fish"></i>
                    <h1>Aquaknow</h1>
                </div>
                <p class="tagline">🌊 Dive into the World of Marine Life 🐠</p>
            </div>

            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i> Username
                    </label>
                    <input type="text" id="username" name="username" required placeholder="Enter your username">
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <input type="password" id="password" name="password" required placeholder="Enter your password">
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i> Dive In
                </button>
            </form>

            <div class="demo-accounts">
                <h3>🎯 Demo Accounts</h3>
                <div class="demo-cards">
                    <div class="demo-card">
                        <h4><i class="fas fa-user"></i> Regular User</h4>
                        <p><strong>Username:</strong> user</p>
                        <p><strong>Password:</strong> password123</p>
                        <small>Access fish identification and educational content</small>
                    </div>
                    <div class="demo-card admin">
                        <h4><i class="fas fa-user-shield"></i> Administrator</h4>
                        <p><strong>Username:</strong> admin</p>
                        <p><strong>Password:</strong> admin123</p>
                        <small>Full access including fish data management</small>
                    </div>
                </div>
            </div>

            <div class="platform-features">
                <h3>🌟 Platform Features</h3>
                <div class="features-grid">
                    <div class="feature">
                        <i class="fas fa-search"></i>
                        <span>Fish Identification</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-images"></i>
                        <span>Ocean Gallery</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-graduation-cap"></i>
                        <span>Marine Education</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-cog"></i>
                        <span>Admin Panel</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="floating-fish">
            <div class="fish fish1">🐠</div>
            <div class="fish fish2">🐟</div>
            <div class="fish fish3">🦈</div>
            <div class="fish fish4">🐡</div>
            <div class="fish fish5">🦑</div>
        </div>
    </div>

    <footer class="login-footer">
        <p>&copy; 2024 Aquaknow Platform. Exploring Marine Life Together 🌊</p>
    </footer>

    <script src="data.js"></script>
    <script src="script.js"></script>
</body>
</html>

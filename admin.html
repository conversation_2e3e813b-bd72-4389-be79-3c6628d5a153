<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Aquaknow Platform</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Admin Navigation -->
        <nav class="admin-navbar">
            <div class="nav-brand">
                <i class="fas fa-fish"></i>
                <span>Aquaknow Admin</span>
            </div>
            <div class="nav-menu">
                <a href="dashboard.html" class="nav-link">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
                <button id="logoutBtn" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </nav>

        <!-- Admin Content -->
        <main class="admin-main">
            <div class="admin-header">
                <h1><i class="fas fa-cog"></i> Admin Panel</h1>
                <p>Manage fish data and platform content</p>
            </div>

            <!-- Add New Fish Section -->
            <section class="admin-section">
                <h2><i class="fas fa-plus"></i> Add New Fish</h2>
                <form id="addFishForm" class="fish-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="fishName">Fish Name:</label>
                            <input type="text" id="fishName" required>
                        </div>
                        <div class="form-group">
                            <label for="scientificName">Scientific Name:</label>
                            <input type="text" id="scientificName" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="habitat">Habitat:</label>
                            <input type="text" id="habitat" required>
                        </div>
                        <div class="form-group">
                            <label for="size">Size:</label>
                            <input type="text" id="size" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="diet">Diet:</label>
                            <input type="text" id="diet" required>
                        </div>
                        <div class="form-group">
                            <label for="imageFile">Image File:</label>
                            <select id="imageFile" required>
                                <option value="">Select an image...</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description:</label>
                        <textarea id="description" rows="4" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="facts">Interesting Facts (one per line):</label>
                        <textarea id="facts" rows="4" placeholder="Enter each fact on a new line"></textarea>
                    </div>
                    
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-save"></i> Add Fish
                    </button>
                </form>
            </section>

            <!-- Manage Existing Fish Section -->
            <section class="admin-section">
                <h2><i class="fas fa-edit"></i> Manage Existing Fish</h2>
                <div class="fish-management">
                    <div class="search-bar">
                        <input type="text" id="manageFishSearch" placeholder="Search fish to edit...">
                        <i class="fas fa-search"></i>
                    </div>
                    
                    <div class="fish-list" id="fishManagementList">
                        <!-- Fish management items will be dynamically generated -->
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Edit Fish Modal -->
    <div id="editFishModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-body">
                <h3><i class="fas fa-edit"></i> Edit Fish Information</h3>
                <form id="editFishForm" class="fish-form">
                    <input type="hidden" id="editFishId">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editFishName">Fish Name:</label>
                            <input type="text" id="editFishName" required>
                        </div>
                        <div class="form-group">
                            <label for="editScientificName">Scientific Name:</label>
                            <input type="text" id="editScientificName" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editHabitat">Habitat:</label>
                            <input type="text" id="editHabitat" required>
                        </div>
                        <div class="form-group">
                            <label for="editSize">Size:</label>
                            <input type="text" id="editSize" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editDiet">Diet:</label>
                            <input type="text" id="editDiet" required>
                        </div>
                        <div class="form-group">
                            <label for="editImageFile">Image File:</label>
                            <select id="editImageFile" required>
                                <option value="">Select an image...</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="editDescription">Description:</label>
                        <textarea id="editDescription" rows="4" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="editFacts">Interesting Facts (one per line):</label>
                        <textarea id="editFacts" rows="4"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-save"></i> Update Fish
                        </button>
                        <button type="button" id="deleteFishBtn" class="delete-btn">
                            <i class="fas fa-trash"></i> Delete Fish
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="data.js"></script>
    <script src="admin.js"></script>
</body>
</html>

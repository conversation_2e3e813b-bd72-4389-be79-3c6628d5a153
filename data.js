// Fish data storage and management
class FishDatabase {
    constructor() {
        this.storageKey = 'aquaknow_fish_data';
        this.init();
    }

    init() {
        // Initialize with default fish data if none exists
        if (!localStorage.getItem(this.storageKey)) {
            this.setDefaultData();
        }
    }

    setDefaultData() {
        const defaultFish = [
            {
                id: 1,
                name: "Tila<PERSON>",
                scientificName: "Oreochromis niloticus",
                habitat: "Freshwater lakes, rivers, and ponds",
                habitatType: "freshwater",
                size: "15-30 cm",
                diet: "Omnivore - algae, plants, small fish",
                image: "images/tilapia.jpg",
                description: "Tilapia is a freshwater fish that is widely cultivated for food. It's known for its mild taste and white flesh. Originally from Africa, tilapia has been introduced to many countries and is now one of the most important aquaculture species worldwide.",
                facts: [
                    "Tilapia can survive in both fresh and saltwater",
                    "They are excellent parents and protect their young",
                    "Can tolerate low oxygen levels better than most fish",
                    "One of the most farmed fish species globally"
                ]
            },
            {
                id: 2,
                name: "Milkfish (Bangus)",
                scientificName: "Chanos chanos",
                habitat: "Coastal waters, estuaries, and aquaculture ponds",
                habitatType: "saltwater",
                size: "100-180 cm",
                diet: "Herbivore - algae, plankton, small invertebrates",
                image: "images/milkfish.jpg",
                description: "Milkfish, locally known as Bangus in the Philippines, is the national fish of the Philippines. It's a large, silvery fish that's highly valued for its taste and nutritional value. Milkfish farming is a major industry in Southeast Asia.",
                facts: [
                    "Can live in both freshwater and saltwater",
                    "National fish of the Philippines",
                    "Can grow up to 1.8 meters in length",
                    "Important source of protein in Southeast Asia"
                ]
            },
            {
                id: 3,
                name: "Lapu-Lapu (Grouper)",
                scientificName: "Epinephelus lanceolatus",
                habitat: "Coral reefs and rocky areas in tropical waters",
                habitatType: "tropical",
                size: "30-200 cm",
                diet: "Carnivore - fish, crustaceans, cephalopods",
                image: "images/lapu-lapu.jpg",
                description: "Lapu-Lapu is a type of grouper fish highly prized in Filipino cuisine. Named after the Filipino hero Lapu-Lapu, this fish is known for its firm, white flesh and excellent taste. It's commonly found in coral reefs and is a popular target for both commercial and recreational fishing.",
                facts: [
                    "Named after the Filipino hero Lapu-Lapu",
                    "Can change color to blend with surroundings",
                    "Some species can live over 40 years",
                    "Highly valued in Asian cuisine"
                ]
            },
            {
                id: 4,
                name: "Snakehead (Dalag)",
                scientificName: "Channa striata",
                habitat: "Freshwater rivers, lakes, and swamps",
                habitatType: "freshwater",
                size: "30-100 cm",
                diet: "Carnivore - fish, frogs, insects, small mammals",
                image: "images/Dalag (Snakehead).jpg",
                description: "The Snakehead, locally known as Dalag, is a predatory freshwater fish native to Southeast Asia. It's known for its ability to breathe air and survive out of water for extended periods. This hardy fish is both feared and respected for its aggressive nature and survival abilities.",
                facts: [
                    "Can breathe air and survive out of water for hours",
                    "Excellent parental care - guards eggs and fry",
                    "Can walk short distances on land",
                    "Considered invasive in some countries"
                ]
            },
            {
                id: 5,
                name: "Tuna",
                scientificName: "Thunnus albacares",
                habitat: "Open ocean waters worldwide",
                habitatType: "saltwater",
                size: "100-200 cm",
                diet: "Carnivore - fish, squid, crustaceans",
                image: "images/tuna.jpg",
                description: "Tuna are large, fast-swimming fish found in warm ocean waters around the world. They are highly valued for their meat and are important both commercially and recreationally. Yellowfin tuna is one of the most popular species for sashimi and canned tuna.",
                facts: [
                    "Can swim at speeds up to 75 km/h",
                    "Warm-blooded fish with high body temperature",
                    "Can dive to depths of over 250 meters",
                    "Highly migratory species traveling thousands of kilometers"
                ]
            },
            {
                id: 6,
                name: "Mackerel",
                scientificName: "Scomber scombrus",
                habitat: "Coastal and offshore waters",
                habitatType: "saltwater",
                size: "25-40 cm",
                diet: "Carnivore - small fish, plankton, crustaceans",
                image: "images/makarel.jpg",
                description: "Mackerel are fast-swimming, streamlined fish found in temperate and tropical seas worldwide. They are known for their distinctive wavy patterns on their backs and are an important commercial fish species. Rich in omega-3 fatty acids, they are considered very nutritious.",
                facts: [
                    "Form large schools for protection",
                    "Rich in omega-3 fatty acids",
                    "Can live up to 20 years",
                    "Important commercial fish species globally"
                ]
            }
        ];

        localStorage.setItem(this.storageKey, JSON.stringify(defaultFish));
    }

    getAllFish() {
        const data = localStorage.getItem(this.storageKey);
        return data ? JSON.parse(data) : [];
    }

    getFishById(id) {
        const allFish = this.getAllFish();
        return allFish.find(fish => fish.id === parseInt(id));
    }

    addFish(fishData) {
        const allFish = this.getAllFish();
        const newId = Math.max(...allFish.map(f => f.id), 0) + 1;
        const newFish = { ...fishData, id: newId };
        allFish.push(newFish);
        localStorage.setItem(this.storageKey, JSON.stringify(allFish));
        return newFish;
    }

    updateFish(id, fishData) {
        const allFish = this.getAllFish();
        const index = allFish.findIndex(fish => fish.id === parseInt(id));
        if (index !== -1) {
            allFish[index] = { ...fishData, id: parseInt(id) };
            localStorage.setItem(this.storageKey, JSON.stringify(allFish));
            return allFish[index];
        }
        return null;
    }

    deleteFish(id) {
        const allFish = this.getAllFish();
        const filteredFish = allFish.filter(fish => fish.id !== parseInt(id));
        localStorage.setItem(this.storageKey, JSON.stringify(filteredFish));
        return true;
    }

    searchFish(query) {
        const allFish = this.getAllFish();
        const searchTerm = query.toLowerCase();
        return allFish.filter(fish =>
            fish.name.toLowerCase().includes(searchTerm) ||
            fish.scientificName.toLowerCase().includes(searchTerm) ||
            fish.habitat.toLowerCase().includes(searchTerm) ||
            fish.description.toLowerCase().includes(searchTerm)
        );
    }
}

// User authentication
class AuthManager {
    constructor() {
        this.storageKey = 'aquaknow_current_user';
        this.users = {
            'user': { password: 'password123', role: 'user' },
            'admin': { password: 'admin123', role: 'admin' }
        };
    }

    login(username, password) {
        const user = this.users[username];
        if (user && user.password === password) {
            const userData = { username, role: user.role };
            localStorage.setItem(this.storageKey, JSON.stringify(userData));
            return userData;
        }
        return null;
    }

    logout() {
        localStorage.removeItem(this.storageKey);
    }

    getCurrentUser() {
        const data = localStorage.getItem(this.storageKey);
        return data ? JSON.parse(data) : null;
    }

    isLoggedIn() {
        return this.getCurrentUser() !== null;
    }

    isAdmin() {
        const user = this.getCurrentUser();
        return user && user.role === 'admin';
    }
}

// Available fish images
const availableImages = [
    'tilapia.jpg',
    'milkfish.jpg',
    'lapu-lapu.jpg',
    'Dalag (Snakehead).jpg',
    'tuna.jpg',
    'makarel.jpg',
    'catfish (hito).jpg',
    'dilis (anchovies).jpg',
    'pufferfish.jpg',
    'salmon.jpg'
];

// Initialize global instances
const fishDB = new FishDatabase();
const authManager = new AuthManager();

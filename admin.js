// Admin panel JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initAdminPanel();
});

function initAdminPanel() {
    // Check if user is logged in and is admin
    if (!authManager.isLoggedIn() || !authManager.isAdmin()) {
        alert('Access denied. Admin privileges required.');
        window.location.href = 'index.html';
        return;
    }

    setupAdminNavigation();
    setupImageSelects();
    setupForms();
    loadFishManagement();
    setupModal();
}

function setupAdminNavigation() {
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            authManager.logout();
            window.location.href = 'index.html';
        });
    }
}

function setupImageSelects() {
    const imageSelects = ['imageFile', 'editImageFile'];
    
    imageSelects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Clear existing options
            select.innerHTML = '<option value="">Select an image...</option>';
            
            // Add available images
            availableImages.forEach(image => {
                const option = document.createElement('option');
                option.value = `images/${image}`;
                option.textContent = image;
                select.appendChild(option);
            });
        }
    });
}

function setupForms() {
    // Add fish form
    const addFishForm = document.getElementById('addFishForm');
    if (addFishForm) {
        addFishForm.addEventListener('submit', handleAddFish);
    }

    // Edit fish form
    const editFishForm = document.getElementById('editFishForm');
    if (editFishForm) {
        editFishForm.addEventListener('submit', handleEditFish);
    }

    // Delete fish button
    const deleteFishBtn = document.getElementById('deleteFishBtn');
    if (deleteFishBtn) {
        deleteFishBtn.addEventListener('click', handleDeleteFish);
    }

    // Search functionality
    const searchInput = document.getElementById('manageFishSearch');
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value;
            const filteredFish = query ? fishDB.searchFish(query) : fishDB.getAllFish();
            displayFishManagement(filteredFish);
        });
    }
}

function setupModal() {
    const modal = document.getElementById('editFishModal');
    const closeBtn = modal.querySelector('.close');
    
    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });
    
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

function handleAddFish(e) {
    e.preventDefault();
    
    const formData = {
        name: document.getElementById('fishName').value,
        scientificName: document.getElementById('scientificName').value,
        habitat: document.getElementById('habitat').value,
        size: document.getElementById('size').value,
        diet: document.getElementById('diet').value,
        image: document.getElementById('imageFile').value,
        description: document.getElementById('description').value,
        facts: document.getElementById('facts').value.split('\n').filter(fact => fact.trim() !== '')
    };

    // Validate required fields
    if (!formData.name || !formData.scientificName || !formData.habitat || 
        !formData.size || !formData.diet || !formData.image || !formData.description) {
        alert('Please fill in all required fields.');
        return;
    }

    // Add fish to database
    const newFish = fishDB.addFish(formData);
    
    if (newFish) {
        alert('Fish added successfully!');
        e.target.reset(); // Clear form
        loadFishManagement(); // Refresh the management list
    } else {
        alert('Error adding fish. Please try again.');
    }
}

function handleEditFish(e) {
    e.preventDefault();
    
    const fishId = document.getElementById('editFishId').value;
    const formData = {
        name: document.getElementById('editFishName').value,
        scientificName: document.getElementById('editScientificName').value,
        habitat: document.getElementById('editHabitat').value,
        size: document.getElementById('editSize').value,
        diet: document.getElementById('editDiet').value,
        image: document.getElementById('editImageFile').value,
        description: document.getElementById('editDescription').value,
        facts: document.getElementById('editFacts').value.split('\n').filter(fact => fact.trim() !== '')
    };

    // Validate required fields
    if (!formData.name || !formData.scientificName || !formData.habitat || 
        !formData.size || !formData.diet || !formData.image || !formData.description) {
        alert('Please fill in all required fields.');
        return;
    }

    // Update fish in database
    const updatedFish = fishDB.updateFish(fishId, formData);
    
    if (updatedFish) {
        alert('Fish updated successfully!');
        document.getElementById('editFishModal').style.display = 'none';
        loadFishManagement(); // Refresh the management list
    } else {
        alert('Error updating fish. Please try again.');
    }
}

function handleDeleteFish() {
    const fishId = document.getElementById('editFishId').value;
    const fishName = document.getElementById('editFishName').value;
    
    if (confirm(`Are you sure you want to delete "${fishName}"? This action cannot be undone.`)) {
        const success = fishDB.deleteFish(fishId);
        
        if (success) {
            alert('Fish deleted successfully!');
            document.getElementById('editFishModal').style.display = 'none';
            loadFishManagement(); // Refresh the management list
        } else {
            alert('Error deleting fish. Please try again.');
        }
    }
}

function loadFishManagement() {
    const allFish = fishDB.getAllFish();
    displayFishManagement(allFish);
}

function displayFishManagement(fishArray) {
    const fishList = document.getElementById('fishManagementList');
    if (!fishList) return;
    
    fishList.innerHTML = '';
    
    if (fishArray.length === 0) {
        fishList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">No fish found.</p>';
        return;
    }
    
    fishArray.forEach(fish => {
        const fishItem = createFishManagementItem(fish);
        fishList.appendChild(fishItem);
    });
}

function createFishManagementItem(fish) {
    const item = document.createElement('div');
    item.className = 'fish-item';
    
    item.innerHTML = `
        <div class="fish-item-info">
            <h4>${fish.name}</h4>
            <p><em>${fish.scientificName}</em> - ${fish.habitat}</p>
        </div>
        <div class="fish-item-actions">
            <button class="edit-btn" onclick="editFish(${fish.id})">
                <i class="fas fa-edit"></i> Edit
            </button>
            <button class="delete-btn" onclick="deleteFishDirect(${fish.id}, '${fish.name}')">
                <i class="fas fa-trash"></i> Delete
            </button>
        </div>
    `;
    
    return item;
}

function editFish(fishId) {
    const fish = fishDB.getFishById(fishId);
    if (!fish) {
        alert('Fish not found.');
        return;
    }
    
    // Populate edit form
    document.getElementById('editFishId').value = fish.id;
    document.getElementById('editFishName').value = fish.name;
    document.getElementById('editScientificName').value = fish.scientificName;
    document.getElementById('editHabitat').value = fish.habitat;
    document.getElementById('editSize').value = fish.size;
    document.getElementById('editDiet').value = fish.diet;
    document.getElementById('editImageFile').value = fish.image;
    document.getElementById('editDescription').value = fish.description;
    document.getElementById('editFacts').value = fish.facts.join('\n');
    
    // Show modal
    document.getElementById('editFishModal').style.display = 'block';
}

function deleteFishDirect(fishId, fishName) {
    if (confirm(`Are you sure you want to delete "${fishName}"? This action cannot be undone.`)) {
        const success = fishDB.deleteFish(fishId);
        
        if (success) {
            alert('Fish deleted successfully!');
            loadFishManagement(); // Refresh the management list
        } else {
            alert('Error deleting fish. Please try again.');
        }
    }
}

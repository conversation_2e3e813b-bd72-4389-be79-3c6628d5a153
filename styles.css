/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* ===== LOGIN PAGE STYLES ===== */
.login-page {
    background: linear-gradient(135deg, #0077be 0%, #00a8cc 25%, #0077be 50%, #004d7a 75%, #002f4b 100%);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.ocean-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100px;
    background: linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0.1) 100%);
    border-radius: 50%;
    animation: wave 6s ease-in-out infinite;
}

.wave1 {
    animation-delay: 0s;
    opacity: 0.7;
}

.wave2 {
    animation-delay: 2s;
    opacity: 0.5;
    height: 80px;
}

.wave3 {
    animation-delay: 4s;
    opacity: 0.3;
    height: 60px;
}

@keyframes wave {
    0%, 100% { transform: translateX(-50%) translateY(0px); }
    50% { transform: translateX(-50%) translateY(-20px); }
}

.login-container {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.logo i {
    font-size: 3rem;
    color: #0077be;
    animation: float 3s ease-in-out infinite;
}

.logo h1 {
    font-size: 2.5rem;
    color: #0077be;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.tagline {
    color: #666;
    font-size: 1.1rem;
    margin-top: 10px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.login-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
}

.form-group label i {
    color: #0077be;
    margin-right: 8px;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-group input:focus {
    outline: none;
    border-color: #0077be;
    box-shadow: 0 0 0 3px rgba(0, 119, 190, 0.1);
    background: white;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #0077be 0%, #00a8cc 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 119, 190, 0.3);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 119, 190, 0.4);
}

.demo-accounts {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(0, 119, 190, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(0, 119, 190, 0.2);
}

.demo-accounts h3 {
    text-align: center;
    color: #0077be;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.demo-cards {
    display: grid;
    gap: 15px;
}

.demo-card {
    background: white;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #0077be;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.demo-card.admin {
    border-left-color: #ff6b6b;
}

.demo-card h4 {
    color: #333;
    margin-bottom: 8px;
    font-size: 1rem;
}

.demo-card p {
    margin: 4px 0;
    font-size: 0.9rem;
}

.demo-card small {
    color: #666;
    font-style: italic;
}

.platform-features {
    text-align: center;
}

.platform-features h3 {
    color: #0077be;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: rgba(0, 119, 190, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.feature:hover {
    background: rgba(0, 119, 190, 0.2);
    transform: translateY(-2px);
}

.feature i {
    font-size: 1.5rem;
    color: #0077be;
    margin-bottom: 8px;
}

.feature span {
    font-size: 0.9rem;
    color: #333;
    font-weight: 500;
}

.floating-fish {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.fish {
    position: absolute;
    font-size: 2rem;
    animation: swim 10s linear infinite;
}

.fish1 {
    top: 20%;
    animation-delay: 0s;
    animation-duration: 12s;
}

.fish2 {
    top: 40%;
    animation-delay: 2s;
    animation-duration: 15s;
}

.fish3 {
    top: 60%;
    animation-delay: 4s;
    animation-duration: 18s;
}

.fish4 {
    top: 80%;
    animation-delay: 6s;
    animation-duration: 14s;
}

.fish5 {
    top: 30%;
    animation-delay: 8s;
    animation-duration: 16s;
}

@keyframes swim {
    0% {
        left: -100px;
        transform: scaleX(1);
    }
    50% {
        transform: scaleX(1);
    }
    51% {
        transform: scaleX(-1);
    }
    100% {
        left: calc(100% + 100px);
        transform: scaleX(-1);
    }
}

.login-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    color: white;
    z-index: 3;
}

.login-footer p {
    margin: 0;
    font-size: 0.9rem;
}

/* ===== DASHBOARD STYLES ===== */
.dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.navbar {
    background: linear-gradient(135deg, #0077be 0%, #00a8cc 100%);
    padding: 15px 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-brand i {
    font-size: 2rem;
    animation: float 3s ease-in-out infinite;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-user span {
    color: white;
    font-weight: 500;
}

.admin-btn,
.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.admin-btn:hover,
.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.dashboard-main {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h2 {
    color: #0077be;
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header p {
    color: #666;
    font-size: 1.2rem;
}

.search-bar {
    position: relative;
    max-width: 500px;
    margin: 0 auto 40px;
}

.search-bar input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 25px;
    font-size: 1.1rem;
    background: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: #0077be;
    box-shadow: 0 4px 20px rgba(0, 119, 190, 0.2);
}

.search-bar i {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #0077be;
    font-size: 1.2rem;
}

.fish-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border-left: 4px solid #0077be;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card i {
    font-size: 2.5rem;
    color: #0077be;
}

.stat-info h3 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
    font-weight: 500;
}

.fish-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
}

.fish-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.fish-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.fish-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.fish-card-content {
    padding: 20px;
}

.fish-card h3 {
    color: #0077be;
    font-size: 1.3rem;
    margin-bottom: 8px;
}

.fish-card .scientific-name {
    color: #666;
    font-style: italic;
    margin-bottom: 10px;
}

.fish-card .habitat-tag {
    display: inline-block;
    background: linear-gradient(135deg, #0077be 0%, #00a8cc 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 10px;
}

.fish-card p {
    color: #666;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* ===== GALLERY STYLES ===== */
.gallery-filters {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.filter-btn {
    background: white;
    color: #0077be;
    border: 2px solid #0077be;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: #0077be;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 119, 190, 0.3);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.gallery-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 20px;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

.gallery-overlay h4 {
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.gallery-overlay p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* ===== EDUCATION STYLES ===== */
.education-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.tab-btn {
    background: white;
    color: #0077be;
    border: 2px solid #0077be;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn:hover,
.tab-btn.active {
    background: #0077be;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 119, 190, 0.3);
}

.education-content {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    min-height: 400px;
}

.species-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.species-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    border-left: 4px solid #0077be;
    transition: all 0.3s ease;
}

.species-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.species-card h4 {
    color: #0077be;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.species-card p {
    color: #666;
    line-height: 1.6;
}

.habitat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.habitat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.habitat-card:hover {
    border-color: #0077be;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.habitat-card i {
    font-size: 3rem;
    color: #0077be;
    margin-bottom: 15px;
}

.habitat-card h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.habitat-card p {
    color: #666;
    line-height: 1.6;
}

.conservation-content {
    text-align: center;
}

.conservation-content h3 {
    color: #0077be;
    font-size: 2rem;
    margin-bottom: 20px;
}

.conservation-content p {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto 30px;
}

.conservation-tips {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 40px;
}

.tip-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    border-top: 4px solid #0077be;
}

.tip-card i {
    font-size: 2rem;
    color: #0077be;
    margin-bottom: 15px;
}

.tip-card h5 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.tip-card p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

.fun-fact {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #f39c12;
    transition: all 0.3s ease;
}

.fun-fact:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.2);
}

.fun-fact h4 {
    color: #d68910;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.fun-fact p {
    color: #333;
    line-height: 1.6;
    margin: 0;
}

/* ===== MODAL STYLES ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    padding: 20px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover,
.close:focus {
    color: #0077be;
}

.modal-body {
    padding: 0 40px 40px;
}

.fish-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

.fish-detail img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.fish-info h3 {
    color: #0077be;
    font-size: 2rem;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.fish-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
}

.fish-details p {
    margin-bottom: 10px;
    color: #333;
}

.fish-details strong {
    color: #0077be;
}

.fish-description,
.fish-facts {
    margin-bottom: 20px;
}

.fish-description h4,
.fish-facts h4 {
    color: #0077be;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.fish-description p {
    color: #666;
    line-height: 1.7;
}

.fish-facts ul {
    list-style: none;
    padding: 0;
}

.fish-facts li {
    background: #e3f2fd;
    padding: 10px 15px;
    margin-bottom: 8px;
    border-radius: 8px;
    border-left: 4px solid #0077be;
    color: #333;
}

.fish-facts li:before {
    content: "🐠 ";
    margin-right: 8px;
}

/* ===== ADMIN PANEL STYLES ===== */
.admin-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.admin-navbar {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 15px 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-navbar .nav-brand {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.admin-navbar .nav-brand i {
    color: #3498db;
    margin-right: 10px;
}

.admin-navbar .nav-menu {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-navbar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.admin-navbar .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.admin-main {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.admin-header {
    text-align: center;
    margin-bottom: 40px;
}

.admin-header h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.admin-header p {
    color: #666;
    font-size: 1.2rem;
}

.admin-section {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.admin-section h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.8rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.fish-form {
    display: grid;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.submit-btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.fish-management {
    margin-top: 20px;
}

.fish-management .search-bar {
    margin-bottom: 25px;
}

.fish-list {
    display: grid;
    gap: 15px;
}

.fish-item {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    border-left: 4px solid #3498db;
}

.fish-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.fish-item-info h4 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.fish-item-info p {
    color: #666;
    margin: 0;
}

.fish-item-actions {
    display: flex;
    gap: 10px;
}

.edit-btn,
.delete-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.edit-btn {
    background: #3498db;
    color: white;
}

.edit-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.delete-btn {
    background: #e74c3c;
    color: white;
}

.delete-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .login-card {
        padding: 30px 20px;
        margin: 20px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .demo-cards {
        grid-template-columns: 1fr;
    }

    .navbar {
        padding: 15px 20px;
    }

    .nav-menu {
        flex-direction: column;
        gap: 10px;
    }

    .dashboard-main {
        padding: 20px;
    }

    .fish-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .fish-detail {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .fish-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .fish-item-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

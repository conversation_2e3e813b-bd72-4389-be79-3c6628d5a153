<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Aquaknow Platform</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-fish"></i>
                <span>Aquaknow</span>
            </div>
            <div class="nav-menu">
                <a href="#" class="nav-link active" data-section="identification">
                    <i class="fas fa-search"></i> Fish Identification
                </a>
                <a href="#" class="nav-link" data-section="gallery">
                    <i class="fas fa-images"></i> Gallery
                </a>
                <a href="#" class="nav-link" data-section="education">
                    <i class="fas fa-book"></i> Education
                </a>
                <div class="nav-user">
                    <span id="currentUser"><i class="fas fa-user"></i> User</span>
                    <button id="adminBtn" class="admin-btn" style="display: none;">
                        <i class="fas fa-cog"></i> Admin
                    </button>
                    <button id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Fish Identification Section -->
            <section id="identification" class="content-section active">
                <div class="section-header">
                    <h2><i class="fas fa-search"></i> Fish Identification</h2>
                    <p>🌊 Discover the amazing fish species in our ocean database 🐠</p>
                </div>

                <div class="search-bar">
                    <input type="text" id="fishSearch" placeholder="🔍 Search for fish species...">
                    <i class="fas fa-search"></i>
                </div>

                <div class="fish-stats">
                    <div class="stat-card">
                        <i class="fas fa-fish"></i>
                        <div class="stat-info">
                            <h3 id="totalFishCount">0</h3>
                            <p>Fish Species</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-water"></i>
                        <div class="stat-info">
                            <h3>3</h3>
                            <p>Habitat Types</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-book-open"></i>
                        <div class="stat-info">
                            <h3 id="totalFactsCount">0</h3>
                            <p>Fun Facts</p>
                        </div>
                    </div>
                </div>

                <div class="fish-grid" id="fishGrid">
                    <!-- Fish cards will be dynamically generated -->
                </div>
            </section>

            <!-- Gallery Section -->
            <section id="gallery" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-images"></i> Ocean Gallery</h2>
                    <p>🌊 Dive into our beautiful collection of marine life 📸</p>
                </div>

                <div class="gallery-filters">
                    <button class="filter-btn active" data-filter="all">🌊 All Fish</button>
                    <button class="filter-btn" data-filter="freshwater">🏞️ Freshwater</button>
                    <button class="filter-btn" data-filter="saltwater">🌊 Saltwater</button>
                    <button class="filter-btn" data-filter="tropical">🏝️ Tropical</button>
                </div>

                <div class="gallery-grid" id="galleryGrid">
                    <!-- Gallery images will be dynamically generated -->
                </div>
            </section>

            <!-- Education Section -->
            <section id="education" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-graduation-cap"></i> Marine Education Center</h2>
                    <p>🎓 Learn fascinating facts about aquatic life and marine ecosystems 🌊</p>
                </div>

                <div class="education-tabs">
                    <button class="tab-btn active" data-tab="species">🐠 Fish Species</button>
                    <button class="tab-btn" data-tab="habitats">🌊 Habitats</button>
                    <button class="tab-btn" data-tab="conservation">🌱 Conservation</button>
                    <button class="tab-btn" data-tab="facts">💡 Fun Facts</button>
                </div>

                <div class="education-content" id="educationContent">
                    <!-- Educational content will be dynamically generated -->
                </div>
            </section>
        </main>
    </div>

    <!-- Fish Detail Modal -->
    <div id="fishModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-body">
                <div class="fish-detail">
                    <img id="modalFishImage" src="" alt="">
                    <div class="fish-info">
                        <h3 id="modalFishName"></h3>
                        <div class="fish-details">
                            <p><strong>Scientific Name:</strong> <span id="modalScientificName"></span></p>
                            <p><strong>Habitat:</strong> <span id="modalHabitat"></span></p>
                            <p><strong>Size:</strong> <span id="modalSize"></span></p>
                            <p><strong>Diet:</strong> <span id="modalDiet"></span></p>
                        </div>
                        <div class="fish-description">
                            <h4>Description:</h4>
                            <p id="modalDescription"></p>
                        </div>
                        <div class="fish-facts">
                            <h4>Interesting Facts:</h4>
                            <ul id="modalFacts"></ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="data.js"></script>
    <script src="script.js"></script>
</body>
</html>

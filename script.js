// Main application JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the login page or dashboard
    if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
        initLoginPage();
    } else if (window.location.pathname.includes('dashboard.html')) {
        initDashboard();
    }
});

// Login Page Functions
function initLoginPage() {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Check if user is already logged in
    if (authManager.isLoggedIn()) {
        window.location.href = 'dashboard.html';
    }
}

function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    const user = authManager.login(username, password);

    if (user) {
        window.location.href = 'dashboard.html';
    } else {
        alert('Invalid username or password. Please try the demo accounts.');
    }
}

// Dashboard Functions
function initDashboard() {
    // Check if user is logged in
    if (!authManager.isLoggedIn()) {
        window.location.href = 'index.html';
        return;
    }

    setupNavigation();
    setupUserInfo();
    setupSearch();
    setupModal();
    loadFishData();
    showSection('identification'); // Show default section
}

function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = link.getAttribute('data-section');
            if (section) {
                showSection(section);

                // Update active nav link
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
            }
        });
    });

    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            authManager.logout();
            window.location.href = 'index.html';
        });
    }

    // Admin button functionality
    const adminBtn = document.getElementById('adminBtn');
    if (adminBtn && authManager.isAdmin()) {
        adminBtn.style.display = 'block';
        adminBtn.addEventListener('click', () => {
            window.location.href = 'admin.html';
        });
    }
}

function setupUserInfo() {
    const currentUser = authManager.getCurrentUser();
    const userElement = document.getElementById('currentUser');
    if (userElement && currentUser) {
        userElement.innerHTML = `<i class="fas fa-user"></i> ${currentUser.username} (${currentUser.role})`;
    }
}

function setupSearch() {
    const searchInput = document.getElementById('fishSearch');
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value;
            const filteredFish = query ? fishDB.searchFish(query) : fishDB.getAllFish();
            displayFishGrid(filteredFish);
        });
    }
}

function setupModal() {
    const modal = document.getElementById('fishModal');
    const closeBtn = modal.querySelector('.close');

    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.classList.remove('active'));

    // Show selected section
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');

        // Load section-specific content
        switch(sectionName) {
            case 'identification':
                loadFishData();
                break;
            case 'gallery':
                loadGallery();
                break;
            case 'education':
                loadEducationContent();
                break;
        }
    }
}

function loadFishData() {
    const allFish = fishDB.getAllFish();
    displayFishGrid(allFish);
    updateStats(allFish);
}

function updateStats(fishArray) {
    // Update total fish count
    const totalFishElement = document.getElementById('totalFishCount');
    if (totalFishElement) {
        totalFishElement.textContent = fishArray.length;
    }

    // Update total facts count
    const totalFactsElement = document.getElementById('totalFactsCount');
    if (totalFactsElement) {
        const totalFacts = fishArray.reduce((total, fish) => total + fish.facts.length, 0);
        totalFactsElement.textContent = totalFacts;
    }
}

function displayFishGrid(fishArray) {
    const fishGrid = document.getElementById('fishGrid');
    if (!fishGrid) return;

    fishGrid.innerHTML = '';

    fishArray.forEach(fish => {
        const fishCard = createFishCard(fish);
        fishGrid.appendChild(fishCard);
    });
}

function createFishCard(fish) {
    const card = document.createElement('div');
    card.className = 'fish-card';
    card.addEventListener('click', () => showFishModal(fish));

    card.innerHTML = `
        <img src="${fish.image}" alt="${fish.name}" onerror="this.src='images/placeholder.jpg'">
        <div class="fish-card-content">
            <h3>${fish.name}</h3>
            <p class="scientific-name">${fish.scientificName}</p>
            <p class="habitat"><i class="fas fa-map-marker-alt"></i> ${fish.habitat}</p>
        </div>
    `;

    return card;
}

function showFishModal(fish) {
    const modal = document.getElementById('fishModal');

    // Populate modal with fish data
    document.getElementById('modalFishImage').src = fish.image;
    document.getElementById('modalFishName').textContent = fish.name;
    document.getElementById('modalScientificName').textContent = fish.scientificName;
    document.getElementById('modalHabitat').textContent = fish.habitat;
    document.getElementById('modalSize').textContent = fish.size;
    document.getElementById('modalDiet').textContent = fish.diet;
    document.getElementById('modalDescription').textContent = fish.description;

    // Populate facts
    const factsList = document.getElementById('modalFacts');
    factsList.innerHTML = '';
    fish.facts.forEach(fact => {
        const li = document.createElement('li');
        li.textContent = fact;
        factsList.appendChild(li);
    });

    modal.style.display = 'block';
}

function loadGallery() {
    setupGalleryFilters();
    showGalleryFilter('all'); // Show all fish by default
}

function setupGalleryFilters() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const filter = btn.getAttribute('data-filter');

            // Update active filter
            filterBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            showGalleryFilter(filter);
        });
    });
}

function showGalleryFilter(filterType) {
    const galleryGrid = document.getElementById('galleryGrid');
    if (!galleryGrid) return;

    let fishToShow = fishDB.getAllFish();

    // Filter fish based on habitat type
    if (filterType !== 'all') {
        fishToShow = fishToShow.filter(fish => fish.habitatType === filterType);
    }

    galleryGrid.innerHTML = '';

    fishToShow.forEach(fish => {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item';
        galleryItem.addEventListener('click', () => showFishModal(fish));

        galleryItem.innerHTML = `
            <img src="${fish.image}" alt="${fish.name}" onerror="this.src='images/placeholder.jpg'">
            <div class="gallery-overlay">
                <h4>${fish.name}</h4>
                <p>${fish.scientificName}</p>
                <div style="margin-top: 10px;">
                    <span style="background: rgba(0,188,212,0.8); padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">
                        ${fish.habitatType}
                    </span>
                </div>
            </div>
        `;

        galleryGrid.appendChild(galleryItem);
    });

    // Show message if no fish found
    if (fishToShow.length === 0) {
        galleryGrid.innerHTML = '<p style="text-align: center; color: white; padding: 40px; font-size: 1.2rem;">No fish found for this habitat type.</p>';
    }
}

function loadEducationContent() {
    setupEducationTabs();
    showEducationTab('species'); // Show default tab
}

function setupEducationTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const tab = btn.getAttribute('data-tab');

            // Update active tab
            tabBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            showEducationTab(tab);
        });
    });
}

function showEducationTab(tabName) {
    const educationContent = document.getElementById('educationContent');
    if (!educationContent) return;

    educationContent.innerHTML = '';

    switch(tabName) {
        case 'species':
            showSpeciesContent(educationContent);
            break;
        case 'habitats':
            showHabitatsContent(educationContent);
            break;
        case 'conservation':
            showConservationContent(educationContent);
            break;
        case 'facts':
            showFactsContent(educationContent);
            break;
    }
}

function showSpeciesContent(container) {
    const allFish = fishDB.getAllFish();

    allFish.forEach(fish => {
        const educationCard = document.createElement('div');
        educationCard.className = 'education-card';

        educationCard.innerHTML = `
            <h3>🐠 ${fish.name} (${fish.scientificName})</h3>
            <p><strong>🌊 Habitat:</strong> ${fish.habitat}</p>
            <p><strong>📏 Size:</strong> ${fish.size}</p>
            <p><strong>🍽️ Diet:</strong> ${fish.diet}</p>
            <p>${fish.description}</p>
            <div style="margin-top: 15px;">
                <strong>💡 Interesting Facts:</strong>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    ${fish.facts.map(fact => `<li>${fact}</li>`).join('')}
                </ul>
            </div>
        `;

        container.appendChild(educationCard);
    });
}

function showHabitatsContent(container) {
    const habitats = [
        {
            name: "🏞️ Freshwater Environments",
            description: "Rivers, lakes, ponds, and streams with low salt content. Home to species like Tilapia and Snakehead.",
            characteristics: ["Low salinity", "Variable temperatures", "Rich in nutrients", "Diverse ecosystems"]
        },
        {
            name: "🌊 Marine Environments",
            description: "Saltwater oceans and seas. Home to species like Tuna, Mackerel, and many tropical fish.",
            characteristics: ["High salinity", "Stable temperatures", "Deep water zones", "Coral reef systems"]
        },
        {
            name: "🏝️ Coastal Waters",
            description: "Areas where freshwater meets saltwater. Many fish like Milkfish can adapt to both environments.",
            characteristics: ["Variable salinity", "Tidal influences", "Rich feeding grounds", "Nursery areas"]
        }
    ];

    habitats.forEach(habitat => {
        const habitatCard = document.createElement('div');
        habitatCard.className = 'habitat-card';

        habitatCard.innerHTML = `
            <h4>${habitat.name}</h4>
            <p>${habitat.description}</p>
            <div style="margin-top: 15px;">
                <strong>Key Characteristics:</strong>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    ${habitat.characteristics.map(char => `<li>${char}</li>`).join('')}
                </ul>
            </div>
        `;

        container.appendChild(habitatCard);
    });
}

function showConservationContent(container) {
    const conservationTips = [
        {
            title: "🌱 Sustainable Fishing",
            content: "Support sustainable fishing practices by choosing fish from well-managed fisheries. Look for certification labels when buying fish."
        },
        {
            title: "🌊 Protect Water Quality",
            content: "Reduce pollution in waterways by properly disposing of chemicals, reducing plastic use, and supporting clean water initiatives."
        },
        {
            title: "🏞️ Habitat Conservation",
            content: "Support the protection of marine and freshwater habitats. Coral reefs, wetlands, and rivers are crucial for fish survival."
        },
        {
            title: "📚 Education & Awareness",
            content: "Learn about local fish species and share knowledge with others. Education is key to conservation efforts."
        }
    ];

    conservationTips.forEach(tip => {
        const tipCard = document.createElement('div');
        tipCard.className = 'conservation-tip';

        tipCard.innerHTML = `
            <h4>${tip.title}</h4>
            <p>${tip.content}</p>
        `;

        container.appendChild(tipCard);
    });
}

function showFactsContent(container) {
    const allFish = fishDB.getAllFish();
    const allFacts = [];

    allFish.forEach(fish => {
        fish.facts.forEach(fact => {
            allFacts.push({
                fishName: fish.name,
                fact: fact
            });
        });
    });

    // Shuffle facts for variety
    const shuffledFacts = allFacts.sort(() => Math.random() - 0.5);

    shuffledFacts.forEach((item, index) => {
        const factCard = document.createElement('div');
        factCard.className = 'fun-fact';

        factCard.innerHTML = `
            <h4>💡 Fun Fact #${index + 1}</h4>
            <p><strong>${item.fishName}:</strong> ${item.fact}</p>
        `;

        container.appendChild(factCard);
    });
}
